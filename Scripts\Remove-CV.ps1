<#
.SYNOPSIS
    Removes Salient Security Platform software components, files, and registry entries.

.DESCRIPTION
    This script provides a comprehensive cleanup solution for Salient Security Platform installations.
    It removes program files, application data, registry entries, and can optionally stop related services.

    Features:
    - Administrator privilege verification
    - Comprehensive error handling and logging
    - Dry-run mode for preview
    - Service management
    - Registry backup before deletion
    - Progress indicators and colored output
    - Detailed cleanup summary

.PARAMETER DryRun
    Preview what would be deleted without actually performing the cleanup.

.PARAMETER Force
    Skip confirmation prompts and proceed with cleanup automatically.

.PARAMETER BackupRegistry
    Create backup of registry keys before deletion (default: $true).

.PARAMETER LogPath
    Specify custom log file path. Default: C:\ProgramData\SalientCleanup\cleanup.log

.PARAMETER Verbose
    Enable verbose output for detailed operation information.

.EXAMPLE
    .\Remove-SalientSecurityPlatform.ps1
    Run interactive cleanup with all safety features enabled.

.EXAMPLE
    .\Remove-SalientSecurityPlatform.ps1 -DryRun
    Preview what would be cleaned up without making changes.

.EXAMPLE
    .\Remove-SalientSecurityPlatform.ps1 -Force -BackupRegistry:$false
    Perform cleanup without prompts and skip registry backup.

.NOTES
    Author: PowerShell Script
    Version: 2.0
    Requires: PowerShell 5.0+, Administrator privileges

.LINK
    https://github.com/your-repo/salient-cleanup
#>

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Preview cleanup without making changes")]
    [switch]$DryRun,

    [Parameter(HelpMessage = "Skip confirmation prompts")]
    [switch]$Force,

    [Parameter(HelpMessage = "Create registry backup before deletion")]
    [bool]$BackupRegistry = $true,

    [Parameter(HelpMessage = "Custom log file path")]
    [string]$LogPath = "C:\ProgramData\SalientCleanup\cleanup.log",

    [Parameter(HelpMessage = "Enable verbose output")]
    [switch]$VerboseOutput
)

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Script configuration
$ErrorActionPreference = 'Continue'
$ProgressPreference = 'Continue'

# Initialize script variables
$script:LogPath = $LogPath
$script:CleanupResults = @{
    FoldersDeleted = @()
    FoldersSkipped = @()
    FoldersFailed = @()
    RegistryDeleted = @()
    RegistrySkipped = @()
    RegistryFailed = @()
    ServicesFound = @()
    ServicesStopped = @()
    ServicesFailed = @()
    BackupPath = ""
    StartTime = Get-Date
    EndTime = $null
}

# Define cleanup targets
$script:Config = @{
    Folders = @(
        "C:\Program Files\Salient Security Platform",
        "C:\ProgramData\Salient Security Platform",
        "$env:APPDATA\Salient Security Platform",
        "$env:LOCALAPPDATA\Salient Security Platform"
    )

    RegistryKeys = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{F4424876-6DF0-4ECB-8AC2-58AF90996F35}",
        "HKLM:\SOFTWARE\Salient Security Platform",
        "HKLM:\SOFTWARE\WOW6432Node\Salient Security Platform",
        "HKCU:\SOFTWARE\Salient Security Platform"
    )

    Services = @(
        "CompleteView Recording Server",
        "CompleteView Management Server",
        "CompleteView Administrative Service"
    )
}

#region Helper Functions

function Initialize-Logging {
    <#
    .SYNOPSIS
        Initialize logging system and create log directory if needed.
    #>
    param([string]$LogPath)

    try {
        $logDir = Split-Path -Path $LogPath -Parent
        if (-not (Test-Path -Path $logDir)) {
            New-Item -Path $logDir -ItemType Directory -Force | Out-Null
            Write-Verbose "Created log directory: $logDir"
        }

        # Initialize log file with header
        $logHeader = @"
================================================================================
Salient Security Platform Cleanup Log
Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
User: $env:USERNAME
Computer: $env:COMPUTERNAME
PowerShell Version: $($PSVersionTable.PSVersion)
================================================================================

"@
        $logHeader | Out-File -FilePath $LogPath -Encoding UTF8
        return $true
    }
    catch {
        Write-Warning "Failed to initialize logging: $_"
        return $false
    }
}

function Write-Log {
    <#
    .SYNOPSIS
        Write message to log file and console with appropriate formatting.
    #>
    param(
        [Parameter(Mandatory)]
        [string]$Message,

        [ValidateSet('Info', 'Warning', 'Error', 'Success', 'Verbose')]
        [string]$Level = 'Info',

        [switch]$NoConsole
    )

    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "[$timestamp] [$Level] $Message"

    # Write to log file
    try {
        $logEntry | Out-File -FilePath $script:LogPath -Append -Encoding UTF8
    }
    catch {
        # Fallback if logging fails
        Write-Warning "Failed to write to log file: $_"
    }

    # Write to console with colors
    if (-not $NoConsole) {
        switch ($Level) {
            'Info'    { Write-Host $Message -ForegroundColor White }
            'Warning' { Write-Host $Message -ForegroundColor Yellow }
            'Error'   { Write-Host $Message -ForegroundColor Red }
            'Success' { Write-Host $Message -ForegroundColor Green }
            'Verbose' { if ($VerboseOutput) { Write-Host $Message -ForegroundColor Cyan } }
        }
    }
}

function Test-AdministratorPrivileges {
    <#
    .SYNOPSIS
        Verify the script is running with administrator privileges.
    #>
    try {
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

        if (-not $isAdmin) {
            Write-Log "Administrator privileges required. Please run as Administrator." -Level Error
            return $false
        }

        Write-Log "Administrator privileges verified." -Level Success
        return $true
    }
    catch {
        Write-Log "Failed to verify administrator privileges: $_" -Level Error
        return $false
    }
}

function Show-Banner {
    <#
    .SYNOPSIS
        Display script banner and information.
    #>
    $banner = @"

╔══════════════════════════════════════════════════════════════════════════════╗
║                    Salient Security Platform Cleanup Tool                   ║
║                                Version 2.0                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝

"@
    Write-Host $banner -ForegroundColor Cyan

    if ($DryRun) {
        Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Yellow -BackgroundColor DarkBlue
    }

    Write-Log "Salient Security Platform Cleanup Tool v2.0 started" -Level Info
    Write-Log "Mode: $(if ($DryRun) { 'Dry Run' } else { 'Live Cleanup' })" -Level Info
}

function Get-UserConfirmation {
    <#
    .SYNOPSIS
        Get user confirmation before proceeding with cleanup.
    #>
    param([string]$Message = "Do you want to proceed with the cleanup?")

    if ($Force) {
        Write-Log "Force mode enabled - skipping confirmation" -Level Verbose
        return $true
    }

    Write-Host "`n$Message" -ForegroundColor Yellow
    Write-Host "Type 'YES' to confirm or any other key to cancel: " -ForegroundColor Yellow -NoNewline

    $response = Read-Host
    $confirmed = $response -eq 'YES'

    Write-Log "User confirmation: $(if ($confirmed) { 'Confirmed' } else { 'Cancelled' })" -Level Info
    return $confirmed
}

#endregion
#region Service Management

function Get-SalientServices {
    <#
    .SYNOPSIS
        Find all Salient-related services on the system.
    #>
    Write-Log "Scanning for Salient Security Platform services..." -Level Info

    $foundServices = @()
    foreach ($serviceName in $script:Config.Services) {
        try {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            if ($service) {
                $foundServices += $service
                $script:CleanupResults.ServicesFound += $serviceName
                Write-Log "Found service: $serviceName (Status: $($service.Status))" -Level Info
            }
            else {
                Write-Log "Service not found: $serviceName" -Level Verbose
            }
        }
        catch {
            Write-Log "Error checking service $serviceName`: $_" -Level Warning
        }
    }

    return $foundServices
}

function Stop-SalientServices {
    <#
    .SYNOPSIS
        Stop all running Salient services with progress tracking.
    #>
    param([array]$Services)

    if ($Services.Count -eq 0) {
        Write-Log "No services to stop." -Level Info
        return $true
    }

    Write-Log "Stopping Salient Security Platform services..." -Level Info
    $success = $true

    foreach ($service in $Services) {
        if ($service.Status -eq 'Running') {
            try {
                if (-not $DryRun) {
                    Write-Log "Stopping service: $($service.Name)" -Level Info
                    Stop-Service -Name $service.Name -Force -ErrorAction Stop

                    # Wait for service to stop with timeout
                    $timeout = 30
                    $timer = 0
                    do {
                        Start-Sleep -Seconds 1
                        $timer++
                        $currentService = Get-Service -Name $service.Name
                    } while ($currentService.Status -eq 'Running' -and $timer -lt $timeout)

                    if ($currentService.Status -eq 'Stopped') {
                        $script:CleanupResults.ServicesStopped += $service.Name
                        Write-Log "Successfully stopped service: $($service.Name)" -Level Success
                    }
                    else {
                        throw "Service did not stop within timeout period"
                    }
                }
                else {
                    Write-Log "[DRY RUN] Would stop service: $($service.Name)" -Level Info
                }
            }
            catch {
                $script:CleanupResults.ServicesFailed += $service.Name
                Write-Log "Failed to stop service $($service.Name): $_" -Level Error
                $success = $false
            }
        }
        else {
            Write-Log "Service $($service.Name) is already stopped" -Level Verbose
        }
    }

    return $success
}

#endregion

#region Registry Management

function Backup-RegistryKeys {
    <#
    .SYNOPSIS
        Create backup of registry keys before deletion.
    #>
    if (-not $BackupRegistry) {
        Write-Log "Registry backup disabled by parameter" -Level Verbose
        return $true
    }

    $backupDir = Join-Path -Path (Split-Path $script:LogPath -Parent) -ChildPath "RegistryBackup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

    try {
        if (-not $DryRun) {
            New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
            $script:CleanupResults.BackupPath = $backupDir
        }

        Write-Log "Creating registry backup in: $backupDir" -Level Info

        foreach ($regKey in $script:Config.RegistryKeys) {
            try {
                if (Test-Path $regKey) {
                    $keyName = ($regKey -split '\\')[-1] -replace '[^\w\-_]', '_'
                    $backupFile = Join-Path -Path $backupDir -ChildPath "$keyName.reg"

                    if (-not $DryRun) {
                        # Convert PowerShell path to reg.exe format
                        $regPath = $regKey -replace '^HKLM:', 'HKEY_LOCAL_MACHINE' -replace '^HKCU:', 'HKEY_CURRENT_USER'
                        $regArgs = @('export', $regPath, $backupFile, '/y')

                        $process = Start-Process -FilePath 'reg.exe' -ArgumentList $regArgs -Wait -PassThru -WindowStyle Hidden
                        if ($process.ExitCode -eq 0) {
                            Write-Log "Backed up registry key: $regKey" -Level Success
                        }
                        else {
                            Write-Log "Failed to backup registry key: $regKey" -Level Warning
                        }
                    }
                    else {
                        Write-Log "[DRY RUN] Would backup registry key: $regKey" -Level Info
                    }
                }
            }
            catch {
                Write-Log "Error backing up registry key $regKey`: $_" -Level Warning
            }
        }

        return $true
    }
    catch {
        Write-Log "Failed to create registry backup: $_" -Level Error
        return $false
    }
}

#endregion
#region Cleanup Functions

function Remove-SalientFolders {
    <#
    .SYNOPSIS
        Remove Salient Security Platform folders with comprehensive error handling.
    #>
    Write-Log "`n--- Cleaning Folders ---" -Level Info

    $totalFolders = $script:Config.Folders.Count
    $currentFolder = 0

    foreach ($folder in $script:Config.Folders) {
        $currentFolder++
        $expandedPath = [Environment]::ExpandEnvironmentVariables($folder)

        Write-Progress -Activity "Cleaning Folders" -Status "Processing: $expandedPath" -PercentComplete (($currentFolder / $totalFolders) * 100)
        Write-Log "Processing folder ($currentFolder/$totalFolders): $expandedPath" -Level Verbose

        if (Test-Path -Path $expandedPath) {
            try {
                # Check if folder is in use
                $folderInfo = Get-Item -Path $expandedPath -ErrorAction Stop
                $folderSize = (Get-ChildItem -Path $expandedPath -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                $folderSizeMB = [math]::Round($folderSize / 1MB, 2)

                Write-Log "Found folder: $expandedPath (Size: $folderSizeMB MB)" -Level Info

                if (-not $DryRun) {
                    # Attempt to remove with retry logic
                    $maxRetries = 3
                    $retryCount = 0
                    $removed = $false

                    do {
                        try {
                            Remove-Item -Path $expandedPath -Recurse -Force -ErrorAction Stop
                            $removed = $true
                            $script:CleanupResults.FoldersDeleted += $expandedPath
                            Write-Log "Successfully deleted folder: $expandedPath" -Level Success
                        }
                        catch {
                            $retryCount++
                            if ($retryCount -lt $maxRetries) {
                                Write-Log "Retry $retryCount/$maxRetries for folder: $expandedPath" -Level Warning
                                Start-Sleep -Seconds 2
                            }
                            else {
                                throw $_
                            }
                        }
                    } while (-not $removed -and $retryCount -lt $maxRetries)

                    if (-not $removed) {
                        throw "Failed after $maxRetries attempts"
                    }
                }
                else {
                    Write-Log "[DRY RUN] Would delete folder: $expandedPath (Size: $folderSizeMB MB)" -Level Info
                }
            }
            catch {
                $script:CleanupResults.FoldersFailed += $expandedPath
                $errorDetails = $_.Exception.Message
                if ($_.Exception.InnerException) {
                    $errorDetails += " Inner: $($_.Exception.InnerException.Message)"
                }
                Write-Log "Failed to delete folder $expandedPath`: $errorDetails" -Level Error

                # Categorize error type
                if ($errorDetails -match "access.*denied|unauthorized") {
                    Write-Log "  → Permission denied. Ensure running as Administrator." -Level Error
                }
                elseif ($errorDetails -match "in use|being used") {
                    Write-Log "  → Files in use. Stop related processes and try again." -Level Error
                }
                elseif ($errorDetails -match "not found|does not exist") {
                    Write-Log "  → Path not found during deletion (may have been partially removed)." -Level Warning
                }
            }
        }
        else {
            $script:CleanupResults.FoldersSkipped += $expandedPath
            Write-Log "Folder does not exist: $expandedPath" -Level Verbose
        }
    }

    Write-Progress -Activity "Cleaning Folders" -Completed
}

function Remove-SalientRegistryKeys {
    <#
    .SYNOPSIS
        Remove Salient Security Platform registry keys with backup and error handling.
    #>
    Write-Log "`n--- Cleaning Registry Keys ---" -Level Info

    $totalKeys = $script:Config.RegistryKeys.Count
    $currentKey = 0

    foreach ($regKey in $script:Config.RegistryKeys) {
        $currentKey++
        Write-Progress -Activity "Cleaning Registry" -Status "Processing: $regKey" -PercentComplete (($currentKey / $totalKeys) * 100)
        Write-Log "Processing registry key ($currentKey/$totalKeys): $regKey" -Level Verbose

        if (Test-Path -Path $regKey) {
            try {
                # Get key information
                $keyInfo = Get-Item -Path $regKey -ErrorAction Stop
                $subKeyCount = $keyInfo.SubKeyCount
                $valueCount = $keyInfo.ValueCount

                Write-Log "Found registry key: $regKey (SubKeys: $subKeyCount, Values: $valueCount)" -Level Info

                if (-not $DryRun) {
                    Remove-Item -Path $regKey -Recurse -Force -ErrorAction Stop
                    $script:CleanupResults.RegistryDeleted += $regKey
                    Write-Log "Successfully deleted registry key: $regKey" -Level Success
                }
                else {
                    Write-Log "[DRY RUN] Would delete registry key: $regKey" -Level Info
                }
            }
            catch {
                $script:CleanupResults.RegistryFailed += $regKey
                $errorDetails = $_.Exception.Message
                Write-Log "Failed to delete registry key $regKey`: $errorDetails" -Level Error

                # Categorize error type
                if ($errorDetails -match "access.*denied|unauthorized") {
                    Write-Log "  → Permission denied. Ensure running as Administrator." -Level Error
                }
                elseif ($errorDetails -match "in use|being used") {
                    Write-Log "  → Registry key in use. Stop related processes and try again." -Level Error
                }
            }
        }
        else {
            $script:CleanupResults.RegistrySkipped += $regKey
            Write-Log "Registry key does not exist: $regKey" -Level Verbose
        }
    }

    Write-Progress -Activity "Cleaning Registry" -Completed
}

#endregion
#region Reporting and Summary

function Show-CleanupSummary {
    <#
    .SYNOPSIS
        Display comprehensive cleanup summary with statistics and recommendations.
    #>
    $script:CleanupResults.EndTime = Get-Date
    $duration = $script:CleanupResults.EndTime - $script:CleanupResults.StartTime

    Write-Host "`n" -NoNewline
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                              CLEANUP SUMMARY                                ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan

    # Execution Summary
    Write-Host "`n📊 EXECUTION SUMMARY" -ForegroundColor Yellow
    Write-Host "   Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White
    Write-Host "   Mode: $(if ($DryRun) { 'Dry Run (Preview)' } else { 'Live Cleanup' })" -ForegroundColor White
    Write-Host "   Log File: $script:LogPath" -ForegroundColor White
    if ($script:CleanupResults.BackupPath) {
        Write-Host "   Registry Backup: $($script:CleanupResults.BackupPath)" -ForegroundColor White
    }

    # Services Summary
    Write-Host "`n🔧 SERVICES" -ForegroundColor Yellow
    Write-Host "   Found: $($script:CleanupResults.ServicesFound.Count)" -ForegroundColor White
    Write-Host "   Stopped: $($script:CleanupResults.ServicesStopped.Count)" -ForegroundColor Green
    Write-Host "   Failed: $($script:CleanupResults.ServicesFailed.Count)" -ForegroundColor Red

    if ($script:CleanupResults.ServicesStopped.Count -gt 0) {
        Write-Host "   Successfully Stopped:" -ForegroundColor Green
        foreach ($service in $script:CleanupResults.ServicesStopped) {
            Write-Host "     • $service" -ForegroundColor Green
        }
    }

    if ($script:CleanupResults.ServicesFailed.Count -gt 0) {
        Write-Host "   Failed to Stop:" -ForegroundColor Red
        foreach ($service in $script:CleanupResults.ServicesFailed) {
            Write-Host "     • $service" -ForegroundColor Red
        }
    }

    # Folders Summary
    Write-Host "`n📁 FOLDERS" -ForegroundColor Yellow
    Write-Host "   Deleted: $($script:CleanupResults.FoldersDeleted.Count)" -ForegroundColor Green
    Write-Host "   Skipped: $($script:CleanupResults.FoldersSkipped.Count)" -ForegroundColor Gray
    Write-Host "   Failed: $($script:CleanupResults.FoldersFailed.Count)" -ForegroundColor Red

    if ($script:CleanupResults.FoldersDeleted.Count -gt 0) {
        Write-Host "   Successfully Deleted:" -ForegroundColor Green
        foreach ($folder in $script:CleanupResults.FoldersDeleted) {
            Write-Host "     • $folder" -ForegroundColor Green
        }
    }

    if ($script:CleanupResults.FoldersFailed.Count -gt 0) {
        Write-Host "   Failed to Delete:" -ForegroundColor Red
        foreach ($folder in $script:CleanupResults.FoldersFailed) {
            Write-Host "     • $folder" -ForegroundColor Red
        }
    }

    # Registry Summary
    Write-Host "`n🗂️  REGISTRY" -ForegroundColor Yellow
    Write-Host "   Deleted: $($script:CleanupResults.RegistryDeleted.Count)" -ForegroundColor Green
    Write-Host "   Skipped: $($script:CleanupResults.RegistrySkipped.Count)" -ForegroundColor Gray
    Write-Host "   Failed: $($script:CleanupResults.RegistryFailed.Count)" -ForegroundColor Red

    if ($script:CleanupResults.RegistryDeleted.Count -gt 0) {
        Write-Host "   Successfully Deleted:" -ForegroundColor Green
        foreach ($regKey in $script:CleanupResults.RegistryDeleted) {
            Write-Host "     • $regKey" -ForegroundColor Green
        }
    }

    if ($script:CleanupResults.RegistryFailed.Count -gt 0) {
        Write-Host "   Failed to Delete:" -ForegroundColor Red
        foreach ($regKey in $script:CleanupResults.RegistryFailed) {
            Write-Host "     • $regKey" -ForegroundColor Red
        }
    }

    # Overall Status
    $totalErrors = $script:CleanupResults.FoldersFailed.Count + $script:CleanupResults.RegistryFailed.Count + $script:CleanupResults.ServicesFailed.Count
    $totalSuccess = $script:CleanupResults.FoldersDeleted.Count + $script:CleanupResults.RegistryDeleted.Count + $script:CleanupResults.ServicesStopped.Count

    Write-Host "`n🎯 OVERALL STATUS" -ForegroundColor Yellow
    if ($DryRun) {
        Write-Host "   Preview completed successfully" -ForegroundColor Cyan
        Write-Host "   Run without -DryRun to perform actual cleanup" -ForegroundColor Cyan
    }
    elseif ($totalErrors -eq 0) {
        Write-Host "   ✅ Cleanup completed successfully!" -ForegroundColor Green
    }
    elseif ($totalSuccess -gt 0 -and $totalErrors -gt 0) {
        Write-Host "   ⚠️  Cleanup completed with some errors" -ForegroundColor Yellow
        Write-Host "   Check log file for details: $script:LogPath" -ForegroundColor Yellow
    }
    else {
        Write-Host "   ❌ Cleanup failed" -ForegroundColor Red
        Write-Host "   Check log file for details: $script:LogPath" -ForegroundColor Red
    }

    # Recommendations
    if ($totalErrors -gt 0 -or ($DryRun -and ($script:CleanupResults.ServicesFound.Count -gt 0 -or $script:Config.Folders.Count -gt 0))) {
        Write-Host "`n💡 RECOMMENDATIONS" -ForegroundColor Yellow

        if ($script:CleanupResults.ServicesFailed.Count -gt 0) {
            Write-Host "   • Manually stop failed services before retrying" -ForegroundColor Cyan
        }

        if ($script:CleanupResults.FoldersFailed.Count -gt 0) {
            Write-Host "   • Close any applications using Salient files" -ForegroundColor Cyan
            Write-Host "   • Restart computer and retry if files are locked" -ForegroundColor Cyan
        }

        if ($script:CleanupResults.RegistryFailed.Count -gt 0) {
            Write-Host "   • Ensure running as Administrator" -ForegroundColor Cyan
            Write-Host "   • Check if registry keys are protected" -ForegroundColor Cyan
        }

        if ($DryRun) {
            Write-Host "   • Review the preview and run without -DryRun to proceed" -ForegroundColor Cyan
        }
    }

    Write-Log "Cleanup summary completed" -Level Info
}

function Test-CleanupSuccess {
    <#
    .SYNOPSIS
        Verify cleanup was successful by checking if targets still exist.
    #>
    Write-Log "`n--- Verifying Cleanup Success ---" -Level Info

    $remainingItems = @()

    # Check folders
    foreach ($folder in $script:Config.Folders) {
        $expandedPath = [Environment]::ExpandEnvironmentVariables($folder)
        if (Test-Path -Path $expandedPath) {
            $remainingItems += "Folder: $expandedPath"
        }
    }

    # Check registry keys
    foreach ($regKey in $script:Config.RegistryKeys) {
        if (Test-Path -Path $regKey) {
            $remainingItems += "Registry: $regKey"
        }
    }

    if ($remainingItems.Count -eq 0) {
        Write-Log "✅ Verification successful - all targets removed" -Level Success
        return $true
    }
    else {
        Write-Log "⚠️  Verification found remaining items:" -Level Warning
        foreach ($item in $remainingItems) {
            Write-Log "   • $item" -Level Warning
        }
        return $false
    }
}

#endregion
#region Main Execution

function Start-SalientCleanup {
    <#
    .SYNOPSIS
        Main cleanup orchestration function.
    #>
    try {
        # Initialize logging
        if (-not (Initialize-Logging -LogPath $script:LogPath)) {
            Write-Warning "Logging initialization failed, continuing without file logging"
        }

        # Show banner
        Show-Banner

        # Verify administrator privileges
        if (-not (Test-AdministratorPrivileges)) {
            Write-Log "Exiting due to insufficient privileges" -Level Error
            return $false
        }

        # Get user confirmation
        $confirmMessage = if ($DryRun) {
            "Do you want to preview what would be cleaned up?"
        } else {
            "⚠️  WARNING: This will permanently delete Salient Security Platform files and registry entries.`nDo you want to proceed with the cleanup?"
        }

        if (-not (Get-UserConfirmation -Message $confirmMessage)) {
            Write-Log "Cleanup cancelled by user" -Level Info
            return $false
        }

        # Find and manage services
        Write-Log "Starting Salient Security Platform cleanup process..." -Level Info
        $services = Get-SalientServices

        if ($services.Count -gt 0) {
            $serviceConfirm = if ($DryRun) {
                "Preview service management?"
            } else {
                "Found $($services.Count) Salient service(s). Stop them before cleanup?"
            }

            if (Get-UserConfirmation -Message $serviceConfirm) {
                $serviceResult = Stop-SalientServices -Services $services
                if (-not $serviceResult -and -not $DryRun) {
                    Write-Log "Service management failed. Continue anyway? Some files may be locked." -Level Warning
                    if (-not (Get-UserConfirmation -Message "Continue with cleanup despite service failures?")) {
                        Write-Log "Cleanup cancelled due to service failures" -Level Info
                        return $false
                    }
                }
            }
        }

        # Create registry backup
        if (-not $DryRun) {
            $backupResult = Backup-RegistryKeys
            if (-not $backupResult -and $BackupRegistry) {
                Write-Log "Registry backup failed. Continue without backup?" -Level Warning
                if (-not (Get-UserConfirmation -Message "Continue cleanup without registry backup?")) {
                    Write-Log "Cleanup cancelled due to backup failure" -Level Info
                    return $false
                }
            }
        }

        # Perform cleanup
        Remove-SalientFolders
        Remove-SalientRegistryKeys

        # Verify cleanup (only for live runs)
        if (-not $DryRun) {
            Test-CleanupSuccess | Out-Null
        }

        # Show summary
        Show-CleanupSummary

        Write-Log "Cleanup process completed" -Level Info
        return $true
    }
    catch {
        Write-Log "Critical error during cleanup: $_" -Level Error
        Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level Error
        return $false
    }
}

#endregion

#region Script Entry Point

# Main script execution
try {
    # Validate parameters
    if ($LogPath -and -not (Test-Path -Path (Split-Path $LogPath -Parent) -IsValid)) {
        throw "Invalid log path specified: $LogPath"
    }

    # Execute cleanup
    $result = Start-SalientCleanup

    # Set exit code based on result
    if ($result) {
        Write-Host "`n✅ Script completed successfully" -ForegroundColor Green
        if (-not $DryRun) {
            Write-Host "💡 Consider restarting your computer to complete the cleanup" -ForegroundColor Cyan
        }
        exit 0
    }
    else {
        Write-Host "`n❌ Script completed with errors" -ForegroundColor Red
        Write-Host "📋 Check the log file for details: $script:LogPath" -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "`n💥 Critical script error: $_" -ForegroundColor Red
    Write-Host "📋 Check the log file for details: $script:LogPath" -ForegroundColor Yellow
    exit 2
}
finally {
    # Clean up and final message
    if (-not $Force -and -not $DryRun) {
        Write-Host "`nPress any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}

#endregion

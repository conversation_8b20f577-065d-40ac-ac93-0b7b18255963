# Run this as Administrator

# Define folders to delete (use environment variable for AppData)
$folders = @(
    "C:\Program Files\Salient Security Platform",
    "C:\ProgramData\Salient Security Platform",
    "$env:APPDATA\Salient Security Platform"
)

# Define registry keys to remove
$regKeys = @(
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{F4424876-6DF0-4ECB-8AC2-58AF90996F35}",
    "HKLM:\SOFTWARE\Salient Security Platform",
    "HKLM:\SOFTWARE\WOW6432Node\Salient Security Platform"
)

Write-Host "`n--- Deleting Folders ---`n"

foreach ($folder in $folders) {
    if (Test-Path $folder) {
        try {
            Remove-Item -Path $folder -Recurse -Force
            Write-Host "Deleted folder: $folder"
        } catch {
            Write-Warning "Failed to delete folder: $folder - $_"
        }
    } else {
        Write-Host "Folder does not exist: $folder"
    }
}

Write-Host "`n--- Cleaning Registry Keys ---`n"

foreach ($key in $regKeys) {
    if (Test-Path $key) {
        try {
            Remove-Item -Path $key -Recurse -Force
            Write-Host "Deleted registry key: $key"
        } catch {
            Write-Warning "Failed to delete registry key: $key - $_"
        }
    } else {
        Write-Host "Registry key does not exist: $key"
    }
}

Write-Host "`nCleanup complete.`n"
Pause
